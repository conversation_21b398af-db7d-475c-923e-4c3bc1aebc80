// src/domains/alertZones/alertZone.service.ts
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ObjectId, Types } from 'mongoose';
import { User } from '../users/user.schema';
import { H3Service } from 'src/utils/h3.service';
import { ServiceZoneService } from '../serviceZone/serviceZone.service';
import { OrganizationService } from '../organizations/organization.service';
import { AlertZone } from './alertZone.interface';
import { AlertZoneStatusEnum } from 'src/common/enums/AlertZoneStatusEnum';
import { LoggingService } from '../logging/logging.service';
import { LogActionEnum } from 'src/common/enums/LogActionEnum';
import { LogEntityEnum } from 'src/common/enums/LogEntityEnum';

@Injectable()
export class AlertZoneService {
  constructor(
    @InjectModel('alertZone') private alertZoneModel: Model<any>,
    private h3Service: H3Service,
    private serviceZone: ServiceZoneService,
    private organizationService: OrganizationService,
    private loggingService: LoggingService,
  ) {}

  async createAlertZone(alertZoneDto: any, user: User): Promise<AlertZone> {
    const createdAlertZone = new this.alertZoneModel(alertZoneDto);
    createdAlertZone.userId = user._id;

    const serviceZones = await this.validateAndGetServiceZone(createdAlertZone);

    createdAlertZone['serviceZones'] = serviceZones;

    return createdAlertZone.save();
  }

  async createOrganizationAlertZone(alertZoneDto: any, user: User): Promise<AlertZone> {
    const createdAlertZone = new this.alertZoneModel(alertZoneDto);

    if (!user.org_id) {
      throw new BadRequestException('User Does not have an organization');
    }

    const orgId = await this.organizationService.findByAuth0Id(user.org_id);
    if (!orgId) {
      throw new BadRequestException(`Organization with auth0_id ${user.org_id} not found`);
    }

    createdAlertZone.orgId = orgId;

    const serviceZones = await this.validateAndGetServiceZone(createdAlertZone);

    createdAlertZone['serviceZones'] = serviceZones;
    createdAlertZone.latestStatus = 1;

    const savedData = await createdAlertZone.save();

    return savedData
  }

  async validateAndGetServiceZone(createdAlertZone: AlertZone) {
    const h3Indexes = [];
    createdAlertZone.geometry.coordinates[0].map((coordinate) => {
      const [lng, lat] = coordinate;
      const h3Index = this.h3Service.getH3Index(lat, lng, 5);
      const exist = h3Indexes.find((h3) => h3 === h3Index);
      if (!exist) h3Indexes.push(h3Index);
    });

    //find all service zones for the h3 indexes
    const serviceZones = await this.serviceZone.findServiceZonesFromH3Indexes(h3Indexes);

    if (serviceZones.length === 0) {
      console.log('throwing error');
      throw new BadRequestException('Failed to find service zones your account might not have access to this area');
    }

    return serviceZones;
  }

  async updateAlertZoneStatus(id: string, isActive: boolean, updatedBy: string): Promise<any> {
    console.log('updateAlertZoneStatus', id, isActive, updatedBy);

    // Find the alert zone first to get its name and organization
    const alertZone = await this.alertZoneModel.findOne({
      _id: new Types.ObjectId(id),
      isDeleted: false
    });

    if (!alertZone) {
      throw new NotFoundException(`Alert zone with ID ${id} not found or already deleted.`);
    }

    // Update the alert zone status
    const result = await this.alertZoneModel
      .updateOne(
        { _id: new Types.ObjectId(id), isDeleted: false },
        {
          $set: {
            isActive,
            updatedAt: new Date(),
            updatedBy: new Types.ObjectId(updatedBy),
          },
        }
      )
      .exec();

    if (result.matchedCount === 0) {
      throw new NotFoundException(`Alert zone with ID ${id} not found or already deleted.`);
    }

    // Log the status change
    let orgId = null;
    if (alertZone.orgId) {
      const organization = await this.organizationService.findByAuth0Id(alertZone.orgId);
      if (organization) {
        orgId = organization.auth0_id;
      }
    }

    await this.loggingService.logAlertZoneStatusChange(
      id,
      isActive,
      updatedBy,
      alertZone.name,
      orgId
    );

    return { message: `Alert zone status updated to ${isActive}.` };
  }

  async findAlertZonesContainingPoint(point: { longitude: number; latitude: number }): Promise<AlertZone[]> {
    return this.alertZoneModel
      .find({
        geometry: {
          $geoIntersects: {
            $geometry: {
              type: 'Point',
              coordinates: [point.longitude, point.latitude],
            },
          },
        },
      })
      .exec();
  }

  async findUsersWithAlertZonesContainingPoint(point: { longitude: number; latitude: number }): Promise<AlertZone[]> {
    const alertZone = await this.alertZoneModel
      .find({
        geometry: {
          $geoIntersects: {
            $geometry: {
              type: 'Point',
              coordinates: [point.longitude, point.latitude],
            },
          },
        },
      })
      .populate('userId')
      .exec();

    return alertZone.map((geofence) => geofence.userId);
  }

  async findZonesForUser(userId: String): Promise<any[]> {
    return this.alertZoneModel
      .find({
        userId: userId,
      })
      .exec();
  }

  async findZonesForOrganization(orgId: String): Promise<any[]> {
    return this.alertZoneModel
      .aggregate([
        {
         $match: {
          isDeleted: false,
         }
        },
        {
          $lookup: {
            from: 'organizations',
            localField: 'orgId',
            foreignField: '_id',
            as: 'organization',
          },
        },
        {
          $match:
            /**
             * query: The query in MQL.
             */
            {
              'organization.auth0_id': orgId,
            },
        },
        {
          $project: {
            name: true,
            geometry: true,
            serviceZones: true,
            latestStatus: true,
            isActive: true
          },
        },
      ])
      .exec();
  }

  async deleteAlertZoneById(id: string, deletedBy: string): Promise<any> {
    // Find the alert zone first to get its name and organization
    const alertZone = await this.alertZoneModel.findOne({
      _id: new Types.ObjectId(id),
      isDeleted: false
    });

    if (!alertZone) {
      throw new NotFoundException(`Alert zone with ID ${id} not found or already deleted.`);
    }

    const result = await this.alertZoneModel
      .updateOne(
        { _id: new Types.ObjectId(id), isDeleted: false }, // Ensure the document is not already deleted
        {
          $set: {
            isDeleted: true,
            isActive: false,
            deletedAt: new Date(),
            deletedBy: new Types.ObjectId(deletedBy),
          },
        }
      )
      .exec();

    if (result.matchedCount === 0) {
      throw new NotFoundException(`Alert zone with ID ${id} not found or already deleted.`);
    }

    // Log the deletion
    let orgId = null;
    if (alertZone.orgId) {
      const organization = await this.organizationService.findByAuth0Id(alertZone.orgId);
      if (organization) {
        orgId = organization.auth0_id;
      }
    }

    await this.loggingService.createLog(
      LogActionEnum.DELETE,
      LogEntityEnum.ALERT_ZONE,
      id,
      deletedBy,
      { name: alertZone.name },
      orgId
    );

    return { message: 'Alert zone soft deleted successfully.' };
  }
}
