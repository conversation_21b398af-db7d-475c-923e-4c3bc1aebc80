import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import AlertZoneSchema from './alertZone.schema';
import { AlertZoneService } from './alertZone.service';
import { ServiceZoneModule } from '../serviceZone/serviceZone.module';
import { OrganizationModule } from '../organizations/organization.module';
import { H3Service } from 'src/utils/h3.service';
import { LoggingModule } from '../logging/logging.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'alertZone', schema: AlertZoneSchema }]),
    ServiceZoneModule,
    OrganizationModule,
    LoggingModule,
  ],
  providers: [AlertZoneService, H3Service],
  exports: [AlertZoneService],
})
export class AlertZoneModule {}
