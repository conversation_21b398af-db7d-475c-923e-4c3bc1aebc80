import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { NotificationService } from './notification.service';
import { NotificationsController } from './notification.controller';
import NotificationSchema from './notification.schema';
import { OrganizationModule } from '../organizations/organization.module';
import Constants from 'src/common/constants';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Constants.notification, schema: NotificationSchema }]),
    OrganizationModule,
  ],
  controllers: [NotificationsController],
  providers: [NotificationService],
  exports: [NotificationService],
})
export class NotificationModule {}
