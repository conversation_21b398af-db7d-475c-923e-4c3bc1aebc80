import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { UserPreferences } from './userPreferences.interface';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { CacheKeyPatterns } from 'src/utils/cache.utils';
import { Cache } from 'cache-manager';

@Injectable()
export class UserPreferencesModel {
  constructor(@InjectModel('userPreferences') private readonly userPreferenceModel: Model<UserPreferences>,
  @Inject(CACHE_MANAGER) private readonly cacheManager: Cache
  ) {}

  async findAll(filter: any): Promise<UserPreferences[]> {
    return this.userPreferenceModel.find(filter).exec();
  }

  async findOne(find: any): Promise<UserPreferences> {
    return this.userPreferenceModel.findOne(find).exec();
  }

  async createOrUpdate(userId: string, userPreference: any, orgId:string): Promise<UserPreferences> {
    const user = await this.userPreferenceModel
      .findOneAndUpdate(
        { user_id: userId },
        {
          ...userPreference,
          userId: userId,
        },
        { new: true, upsert: true }, // Create a new document if it doesn't exist (upsert)
      )
      .exec();

      const cacheKey = CacheKeyPatterns.USER_BY_SUB(userId);
      const cachKeyFromAD = "users_by_org:" + orgId;
      console.log('Invalidating cache for user: ', cacheKey);
      await this.cacheManager.del(cacheKey);
      await this.cacheManager.del(cachKeyFromAD);

    return user;
  }
}
