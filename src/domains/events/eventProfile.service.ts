import { Injectable, NotFoundException, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { EventProfile } from './eventProfile.interface';
import Constants from 'src/common/constants';
import { CacheKeyPatterns } from '../../utils/cache.utils';

@Injectable()
export class EventProfileService {
  // Cache TTL constants (in milliseconds)
  private static readonly CACHE_TTL_EVENT_PROFILE = 300000; // 5 minutes

  constructor(
    @InjectModel(Constants.event_profile) private eventProfileModel: Model<EventProfile>,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {}

  async findAll(skip: number = 0, limit: number = 10, filters?: any): Promise<{ eventProfiles: EventProfile[], total: number }> {
    const query = { isDeleted: false, ...filters };
    
    const [eventProfiles, total] = await Promise.all([
      this.eventProfileModel
        .find(query)
        .skip(skip)
        .limit(limit)
        .sort({ TIME_STAMP: -1 })
        .exec(),
      this.eventProfileModel.countDocuments(query).exec(),
    ]);

    return { eventProfiles, total };
  }

  async findOne(eventId: string): Promise<EventProfile> {
    const cacheKey = CacheKeyPatterns.EVENT_PROFILE_BY_ID(eventId);

    // Try to get from cache first
    const cachedEventProfile = await this.cacheManager.get<EventProfile>(cacheKey);
    if (cachedEventProfile) {
      return cachedEventProfile;
    }

    // If not in cache, query database
    const eventProfile = await this.eventProfileModel.findOne({ 
      EVENT_ID: eventId
    }).exec();

    if (!eventProfile) {
      throw new NotFoundException(`Event profile with ID ${eventId} not found`);
    }

    // Store in cache
    await this.cacheManager.set(cacheKey, eventProfile, EventProfileService.CACHE_TTL_EVENT_PROFILE);

    return eventProfile;
  }

  async findByDeviceId(deviceId: string): Promise<EventProfile[]> {
    const cacheKey = CacheKeyPatterns.EVENT_PROFILE_BY_DEVICE_ID(deviceId);

    // Try to get from cache first
    const cachedEventProfiles = await this.cacheManager.get<EventProfile[]>(cacheKey);
    if (cachedEventProfiles) {
      return cachedEventProfiles;
    }

    // If not in cache, query database
    const eventProfiles = await this.eventProfileModel
      .find({ DEVICE_ID: deviceId, isDeleted: false })
      .sort({ TIME_STAMP: -1 })
      .exec();

    // Store in cache
    if (eventProfiles) {
      await this.cacheManager.set(cacheKey, eventProfiles, EventProfileService.CACHE_TTL_EVENT_PROFILE);
    }

    return eventProfiles;
  }

  async findByDateRange(startDate: Date, endDate: Date, filters?: any): Promise<EventProfile[]> {
    const query = {
      TIME_STAMP: {
        $gte: startDate,
        $lte: endDate,
      },
      isDeleted: false,
      ...filters,
    };

    return this.eventProfileModel
      .find(query)
      .sort({ TIME_STAMP: -1 })
      .exec();
  }

  async create(eventProfileData: Partial<EventProfile>, userId?: string): Promise<EventProfile> {
    const newEventProfile = new this.eventProfileModel({
      ...eventProfileData,
      createdBy: userId ? new Types.ObjectId(userId) : undefined,
      updatedBy: userId ? new Types.ObjectId(userId) : undefined,
    });

    const savedEventProfile = await newEventProfile.save();

    // Invalidate related cache entries
    await this.invalidateEventProfileCache(savedEventProfile.EVENT_ID, savedEventProfile.DEVICE_ID);

    return savedEventProfile;
  }

  async update(eventId: string, eventProfileData: Partial<EventProfile>, userId?: string): Promise<EventProfile> {
    const eventProfile = await this.eventProfileModel.findOne({ 
      EVENT_ID: eventId, 
      isDeleted: false 
    }).exec();

    if (!eventProfile) {
      throw new NotFoundException(`Event profile with ID ${eventId} not found`);
    }

    const updatedEventProfile = await this.eventProfileModel.findOneAndUpdate(
      { EVENT_ID: eventId },
      {
        ...eventProfileData,
        updatedBy: userId ? new Types.ObjectId(userId) : undefined,
        updatedAt: new Date(),
      },
      { new: true }
    ).exec();

    // Invalidate cache
    await this.invalidateEventProfileCache(eventId, eventProfile.DEVICE_ID);

    return updatedEventProfile;
  }

  async delete(eventId: string, userId?: string): Promise<{ message: string }> {
    const eventProfile = await this.eventProfileModel.findOne({ 
      EVENT_ID: eventId, 
      isDeleted: false 
    }).exec();

    if (!eventProfile) {
      throw new NotFoundException(`Event profile with ID ${eventId} not found`);
    }

    // Soft delete
    await this.eventProfileModel.findOneAndUpdate(
      { EVENT_ID: eventId },
      {
        isDeleted: true,
        isActive: false,
        deletedAt: new Date(),
        deletedBy: userId ? new Types.ObjectId(userId) : undefined,
      }
    ).exec();

    // Invalidate cache
    await this.invalidateEventProfileCache(eventId, eventProfile.DEVICE_ID);

    return { message: 'Event profile deleted successfully' };
  }

  async getEventStats(filters?: any): Promise<any> {
    const query = { isDeleted: false, ...filters };

    const stats = await this.eventProfileModel.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          totalEvents: { $sum: 1 },
          avgDuration: { $avg: '$DURATION' },
          uniqueDevices: { $addToSet: '$DEVICE_ID' },
          statusDistribution: {
            $push: '$STATUS'
          }
        }
      },
      {
        $project: {
          _id: 0,
          totalEvents: 1,
          avgDuration: 1,
          uniqueDeviceCount: { $size: '$uniqueDevices' },
          statusDistribution: 1
        }
      }
    ]).exec();

    return stats[0] || {
      totalEvents: 0,
      avgDuration: 0,
      uniqueDeviceCount: 0,
      statusDistribution: []
    };
  }

  /**
   * Helper method to invalidate cache entries related to an event profile
   */
  private async invalidateEventProfileCache(eventId: string, deviceId?: string): Promise<void> {
    try {
      const cacheKeysToDelete = [
        CacheKeyPatterns.EVENT_PROFILE_BY_ID(eventId),
      ];

      if (deviceId) {
        cacheKeysToDelete.push(CacheKeyPatterns.EVENT_PROFILE_BY_DEVICE_ID(deviceId));
      }

      await Promise.all(cacheKeysToDelete.map(key => this.cacheManager.del(key)));
    } catch (error) {
      console.warn(`Failed to invalidate event profile cache: ${error.message}`);
    }
  }
}
