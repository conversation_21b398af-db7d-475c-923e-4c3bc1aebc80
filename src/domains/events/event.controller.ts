import { Controller, Get, Param, Query, Req } from '@nestjs/common';
import { EventService } from './event.service';
import { EventProfileService } from './eventProfile.service';
import { EventHistoryService } from './eventHistory.service';
import { EventProfile } from './eventProfile.interface';
import { EventHistory } from './eventHistory.interface';

@Controller('api/events')
export class EventsController {
  constructor(
    private readonly eventService: EventService,
    private readonly eventProfileService: EventProfileService,
    private readonly eventHistoryService: EventHistoryService,
    // EventModel kept for backward compatibility but not actively used
  ) {}

  @Get('/alertZone/:alertZoneId')
  async findAll(
    @Query('page') page: string = '1',
    @Query('pageSize') pageSize: string = '10',
    @Query('query') query: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Param('alertZoneId') alertZoneId: string,
    @Query('sort') sort: string,
    @Query('sortDirection') sortDirection: string,
    @Req() req: Request,
  ): Promise<any[]> {
    const skip = (parseInt(page) - 1) * parseInt(pageSize);
    const user = req['user'];
    const orgId = user.org_id;
    return await this.eventService.findAll(
      skip,
      parseInt(pageSize),
      orgId,
      alertZoneId,
      query,
      new Date(startDate),
      new Date(endDate),
      sort,
      sortDirection,
    );
  }

  @Get('/count/alertZone')
  async findEventCountsByAlertZones(
    @Query('query') query: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Req() req: Request,
  ): Promise<any[]> {
    const user = req['user'];
    const orgId = user.org_id;
    return await this.eventService.findEventCountsByAlertZones(orgId, query, new Date(startDate), new Date(endDate));
  }

  @Get(':id')
  async findOne(@Param('id') id: string, @Query('size') size: string = '0'): Promise<EventProfile> {
    return await this.eventService.findOne(id, parseInt(size));
  }

  // New endpoints using the modular services

  @Get('profile/:eventId')
  async getEventProfile(@Param('eventId') eventId: string): Promise<EventProfile> {
    return await this.eventProfileService.findOne(eventId);
  }

  @Get('history/:eventId')
  async getEventHistory(
    @Param('eventId') eventId: string,
    @Query('limit') limit: string = '50'
  ): Promise<EventHistory[]> {
    return await this.eventHistoryService.findByEventId(eventId, parseInt(limit));
  }

  @Get('device/:deviceId')
  async getEventsByDevice(@Param('deviceId') deviceId: string): Promise<{
    profiles: EventProfile[];
    history: EventHistory[];
  }> {
    return await this.eventService.getEventsByDeviceId(deviceId);
  }

  @Get('device/:deviceId/profiles')
  async getEventProfilesByDevice(@Param('deviceId') deviceId: string): Promise<EventProfile[]> {
    return await this.eventProfileService.findByDeviceId(deviceId);
  }

  @Get('device/:deviceId/history')
  async getEventHistoryByDevice(
    @Param('deviceId') deviceId: string,
    @Query('limit') limit: string = '100'
  ): Promise<EventHistory[]> {
    return await this.eventHistoryService.findByDeviceId(deviceId, parseInt(limit));
  }

  @Get('range/profiles')
  async getEventProfilesByDateRange(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('skip') skip: string = '0',
    @Query('limit') limit: string = '10'
  ): Promise<{ eventProfiles: EventProfile[], total: number }> {
    const filters = {
      TIME_STAMP: {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      }
    };
    return await this.eventProfileService.findAll(parseInt(skip), parseInt(limit), filters);
  }

  @Get('range/history')
  async getEventHistoryByDateRange(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('skip') skip: string = '0',
    @Query('limit') limit: string = '50'
  ): Promise<{ eventHistory: EventHistory[], total: number }> {
    const filters = {
      TIME_STAMP: {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      }
    };
    return await this.eventHistoryService.findAll(parseInt(skip), parseInt(limit), filters);
  }

  @Get('location/search')
  async searchEventsByLocation(
    @Query('lat') lat: string,
    @Query('lon') lon: string,
    @Query('radius') radius: string = '1'
  ): Promise<EventHistory[]> {
    return await this.eventHistoryService.findByLocation(
      parseFloat(lat),
      parseFloat(lon),
      parseFloat(radius)
    );
  }

  @Get('stats/overview')
  async getEventStatistics(@Query() filters: any): Promise<any> {
    return await this.eventService.getEventStatistics(filters);
  }

  @Get('stats/profiles')
  async getEventProfileStats(@Query() filters: any): Promise<any> {
    return await this.eventProfileService.getEventStats(filters);
  }

  @Get('stats/history')
  async getEventHistoryStats(
    @Query('eventId') eventId?: string,
    @Query('deviceId') deviceId?: string
  ): Promise<any> {
    return await this.eventHistoryService.getEventHistoryStats(eventId, deviceId);
  }

  @Get('latest/:eventId')
  async getLatestEventHistory(@Param('eventId') eventId: string): Promise<EventHistory | null> {
    return await this.eventHistoryService.getLatestByEventId(eventId);
  }
}
