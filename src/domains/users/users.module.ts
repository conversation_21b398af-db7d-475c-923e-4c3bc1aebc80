import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { User, UserSchema } from './user.schema';
import { AlertZoneModule } from '../alertZones/alertZone.module';
import { OrganizationModule } from '../organizations/organization.module';
import { ServiceZoneService } from '../serviceZone/serviceZone.service';
import ServiceZoneSchema from '../serviceZone/serviceZone.schema';
import { UtilsModule } from 'src/utils/UtilsModule';


@Module({
  imports: [
    MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
    AlertZoneModule,
    OrganizationModule,
    MongooseModule.forFeature([{ name: 'servicezones', schema: ServiceZoneSchema }]),
    UtilsModule,
  ],
  controllers: [UsersController],
  providers: [UsersService, ServiceZoneService],
  exports: [UsersService],
})
export class UsersModule {}
