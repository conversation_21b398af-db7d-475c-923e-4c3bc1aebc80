import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { auth, AuthOptions } from 'express-oauth2-jwt-bearer'; // Import AuthOptions
import * as jwt from 'jsonwebtoken'; // Import jwt for decoding JWT tokens
import { UsersService } from 'src/domains/users/users.service';

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  constructor(private readonly userService: UsersService, 
              private readonly logger: Logger
  ) {}
  async use(req: Request, res: Response, next: NextFunction) {
    if (req.baseUrl === '') {
      res.status(200).send('Root path is skipped');
    } else {
    this.logger.log(`AuthMiddleware: ${req.baseUrl}, Method: ${req.method}`);
      
      // next();
      // return;
      const jwtCheck = auth({
        audience: process.env.AUTH0_AUDIENCE,
        issuerBaseURL: process.env.AUTH0_ISSUER_URL,
        tokenSigningAlg: 'RS256',
      });

      // Call jwtCheck and pass a callback to handle the result
      jwtCheck(req, res, async (err) => {
        if (err) {
          // Handle error
          // For example, you can send an error response
          return res.status(401).json({ message: 'Unauthorized', err });
        }

        // If no error, token is verified
        // You can access the decoded token's claims from the req object
        const token = req.headers.authorization.split(' ')[1]; // Assuming token is in the format "Bearer <token>"
        const decodedToken = jwt.decode(token);

        req['sub'] = decodedToken['sub'] as string; //?.split('|')[1];
        req['org_id'] = decodedToken['org_id'] as string;

        // Fetch the user from the database
        const user = await this.userService.findOne({
          sub: req['sub'],
        });

        if (!user && req.baseUrl !== '/users/me') {
          return res.status(404).json({ message: 'User not found' });
        }
        req['user'] = user;

        // Proceed to the next middleware or route handler
        next();
      });
    }
  }
}
