MSK_BROKERS='["b-2-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198","b-1-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198","b-3-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198"]'

# MONGODB_CONNECTION_STRING="mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=AirWardenEssentials"
MONGODB_CONNECTION_STRING="mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=AirWardenEssentials"
DATABASE_NAME=coddn

AWS_ACCESS_KEY_ID=********************
AWS_SERCRET_KEY_ID=QU0vP1ZhjhFJSG3mMI7Ep1N66gHsMp3liiq1pbYy
AWS_REGION=us-east-2

MSK_PYTHON_BROKERS='b-2-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198,b-1-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198'

AUTH0_BASE_URL=http://localhost:3000
# AUTH0_ISSUER_URL=https://dev-mquz0q4oastb8zws.us.auth0.com
AUTH0_ISSUER_URL=https://auth.aerodefense.tech
AUTH0_AUDIENCE=https://api.aerodefense.tech
AUTH0_CLIENT_ID=dEKusms9RMEdR7eWzciVC9uonXDJ5kDS
AUTH0_SECRET_KEY=****************************************************************

AUTH0_MANAGEMENT_API=https://airwarden-essentials.us.auth0.com
AUTH0_MANAGEMENT_API_CLIENT_ID=WPKVzKDeWh23GIDvE2qxT3PcZ7UYTj2l
AUTH0_MANAGEMENT_API_CLIENT_SECRET=****************************************************************


GROUP_ID=prodGroupApi

RID_UI_TOPIC="DETECTION"
RID_TOPIC="RID"

APP_SYNC_URL='https://nzf6alzxl5grfhylui4srzpfka.appsync-api.us-east-2.amazonaws.com/graphql'
APP_SYNC_API_KEY='da2-vcdnqfsqtjgbflxmr3rnhxchba'

# REDIS_HOST="rediss://valkey-connect:<EMAIL>:6379"
ENV=prod
