import logging
import time
from typing import Dict, Any
from utils.CacheService import CacheService

logger = logging.getLogger(__name__)

class CacheMonitor:
    """
    Utility class for monitoring cache performance and health.
    """
    
    def __init__(self):
        self.cache_service = CacheService()
        self.start_time = time.time()
        self.operation_counts = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0
        }
    
    def log_cache_hit(self, key: str):
        """Log a cache hit."""
        self.operation_counts['hits'] += 1
        logger.debug(f"Cache HIT: {key}")
    
    def log_cache_miss(self, key: str):
        """Log a cache miss."""
        self.operation_counts['misses'] += 1
        logger.debug(f"Cache MISS: {key}")
    
    def log_cache_set(self, key: str):
        """Log a cache set operation."""
        self.operation_counts['sets'] += 1
        logger.debug(f"Cache SET: {key}")
    
    def log_cache_delete(self, key: str):
        """Log a cache delete operation."""
        self.operation_counts['deletes'] += 1
        logger.debug(f"Cache DELETE: {key}")
    
    def log_cache_error(self, operation: str, error: str):
        """Log a cache error."""
        self.operation_counts['errors'] += 1
        logger.error(f"Cache ERROR in {operation}: {error}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get cache performance statistics.
        
        Returns:
            dict: Performance statistics
        """
        total_operations = self.operation_counts['hits'] + self.operation_counts['misses']
        hit_rate = (self.operation_counts['hits'] / total_operations * 100) if total_operations > 0 else 0
        
        uptime = time.time() - self.start_time
        
        # Get Redis stats
        redis_stats = self.cache_service.get_cache_stats()
        
        return {
            'uptime_seconds': round(uptime, 2),
            'local_stats': {
                'hits': self.operation_counts['hits'],
                'misses': self.operation_counts['misses'],
                'sets': self.operation_counts['sets'],
                'deletes': self.operation_counts['deletes'],
                'errors': self.operation_counts['errors'],
                'hit_rate_percent': round(hit_rate, 2),
                'total_operations': total_operations
            },
            'redis_stats': redis_stats
        }
    
    def print_performance_report(self):
        """Print a formatted performance report."""
        stats = self.get_performance_stats()
        
        print("\n" + "="*50)
        print("CACHE PERFORMANCE REPORT")
        print("="*50)
        print(f"Uptime: {stats['uptime_seconds']} seconds")
        print(f"Total Operations: {stats['local_stats']['total_operations']}")
        print(f"Cache Hit Rate: {stats['local_stats']['hit_rate_percent']}%")
        print(f"Hits: {stats['local_stats']['hits']}")
        print(f"Misses: {stats['local_stats']['misses']}")
        print(f"Sets: {stats['local_stats']['sets']}")
        print(f"Deletes: {stats['local_stats']['deletes']}")
        print(f"Errors: {stats['local_stats']['errors']}")
        
        if stats['redis_stats']:
            print("\nRedis Stats:")
            print(f"Connected Clients: {stats['redis_stats'].get('connected_clients', 'N/A')}")
            print(f"Used Memory: {stats['redis_stats'].get('used_memory_human', 'N/A')}")
            print(f"Redis Hit Rate: {stats['redis_stats'].get('hit_rate', 'N/A')}%")
        
        print("="*50)
    
    def reset_stats(self):
        """Reset all performance statistics."""
        self.operation_counts = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0
        }
        self.start_time = time.time()
        logger.info("Cache performance statistics reset")
    
    def check_cache_health(self) -> Dict[str, Any]:
        """
        Check the health of the cache system.
        
        Returns:
            dict: Health check results
        """
        health_status = {
            'healthy': True,
            'issues': [],
            'recommendations': []
        }
        
        try:
            # Test basic connectivity
            if not self.cache_service.test_connection():
                health_status['healthy'] = False
                health_status['issues'].append("Cannot connect to cache server")
                return health_status
            
            # Check hit rate
            stats = self.get_performance_stats()
            hit_rate = stats['local_stats']['hit_rate_percent']
            
            if hit_rate < 50 and stats['local_stats']['total_operations'] > 100:
                health_status['issues'].append(f"Low cache hit rate: {hit_rate}%")
                health_status['recommendations'].append("Consider adjusting cache TTL or cache keys")
            
            # Check error rate
            error_rate = (stats['local_stats']['errors'] / max(stats['local_stats']['total_operations'], 1)) * 100
            if error_rate > 5:
                health_status['healthy'] = False
                health_status['issues'].append(f"High error rate: {error_rate}%")
                health_status['recommendations'].append("Check cache server logs and connectivity")
            
            # Check Redis memory usage if available
            redis_stats = stats.get('redis_stats', {})
            if redis_stats and 'used_memory_human' in redis_stats:
                memory_str = redis_stats['used_memory_human']
                if 'G' in memory_str:  # Gigabytes
                    memory_gb = float(memory_str.replace('G', ''))
                    if memory_gb > 1.0:  # More than 1GB
                        health_status['recommendations'].append("High memory usage detected, consider cache cleanup")
            
        except Exception as e:
            health_status['healthy'] = False
            health_status['issues'].append(f"Health check failed: {str(e)}")
        
        return health_status
    
    def log_health_check(self):
        """Perform and log a health check."""
        health = self.check_cache_health()
        
        if health['healthy']:
            logger.info("Cache health check: HEALTHY")
        else:
            logger.warning(f"Cache health check: ISSUES DETECTED - {', '.join(health['issues'])}")
            
        for recommendation in health['recommendations']:
            logger.info(f"Cache recommendation: {recommendation}")
        
        return health
