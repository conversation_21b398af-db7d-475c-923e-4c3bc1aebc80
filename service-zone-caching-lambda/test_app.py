#!/usr/bin/env python3
"""
Test script to demonstrate the service zone caching functionality
"""

import json
from app import lambda_handler, fetch_and_cache_service_zones, get_cached_service_zones

def test_cache_functionality():
    """
    Test the cache functionality without Lambda event
    """
    print("="*60)
    print("TESTING SERVICE ZONE CACHING FUNCTIONALITY")
    print("="*60)
    
    # Test 1: Fetch and cache service zones
    print("\n1. Testing fetch_and_cache_service_zones()...")
    cache_result = fetch_and_cache_service_zones()
    print(f"Cache operation result: {cache_result}")
    
    # Test 2: Retrieve cached service zones
    print("\n2. Testing get_cached_service_zones()...")
    cached_zones = get_cached_service_zones()
    if cached_zones:
        print(f"Successfully retrieved {len(cached_zones)} service zones from cache")
        print("Sample cached zone:")
        if len(cached_zones) > 0:
            sample_zone = cached_zones[0]
            print(json.dumps(sample_zone, indent=2))
    else:
        print("No service zones found in cache")
    
    print("\n" + "="*60)

def test_lambda_handler():
    """
    Test the lambda handler with a sample event
    """
    print("\n\nTESTING LAMBDA HANDLER")
    print("="*60)
    
    # Sample Kafka event
    test_event = {
        'eventSource': 'aws:kafka',
        'records': {
            'SERVICE_ZONE_CACHE_DEV-0': [
                {
                    'topic': 'SERVICE_ZONE_CACHE_DEV',
                    'partition': 0,
                    'offset': 0,
                    'timestamp': 1751485694940,
                    'timestampType': 'CREATE_TIME',
                    'value': 'eyJ0dGwiOiAzNjAwfQ==',  # base64 encoded '{"ttl": 3600}'
                    'headers': []
                }
            ]
        }
    }
    
    # Test lambda handler
    result = lambda_handler(test_event, None)
    print("Lambda handler result:")
    print(json.dumps(result, indent=2))
    
    print("="*60)

if __name__ == "__main__":
    # Run tests
    test_cache_functionality()
    test_lambda_handler()
    
    print("\n\nTest completed! Check the logs above for results.")
