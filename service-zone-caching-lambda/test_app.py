#!/usr/bin/env python3
"""
Test script to demonstrate the service zone caching functionality
"""

import json
from app import lambda_handler, fetch_and_cache_service_zones, get_cached_h3_mapping, test_cache_retrieval

def test_cache_functionality():
    """
    Test the cache functionality without Lambda event
    """
    print("="*60)
    print("TESTING H3 MAPPING CACHING FUNCTIONALITY")
    print("="*60)

    # Test 1: Fetch and cache H3 mappings
    print("\n1. Testing fetch_and_cache_service_zones()...")
    cache_result = fetch_and_cache_service_zones()
    print(f"Cache operation result: {cache_result}")

    # Test 2: Retrieve cached H3 mappings
    print("\n2. Testing cache retrieval...")
    test_results = test_cache_retrieval()

    print("Cache retrieval test results:")
    for h3_id, fips_data in test_results.items():
        if fips_data is not None:
            print(f"  H3 ID {h3_id}: {fips_data}")
        else:
            print(f"  H3 ID {h3_id}: No data found in cache")

    # Test 3: Test individual H3 mapping retrieval
    print("\n3. Testing individual H3 mapping retrieval...")
    sample_h3_id = "87260e535ffffff"
    fips_data = get_cached_h3_mapping(sample_h3_id)
    if fips_data is not None:
        print(f"FIPS data for H3 ID {sample_h3_id}: {fips_data}")
    else:
        print(f"No FIPS data found for H3 ID {sample_h3_id}")

    print("\n" + "="*60)

def test_lambda_handler():
    """
    Test the lambda handler with a sample event
    """
    print("\n\nTESTING LAMBDA HANDLER")
    print("="*60)
    
    # Sample Kafka event
    test_event = {
        'eventSource': 'aws:kafka',
        'records': {
            'SERVICE_ZONE_CACHE_DEV-0': [
                {
                    'topic': 'SERVICE_ZONE_CACHE_DEV',
                    'partition': 0,
                    'offset': 0,
                    'timestamp': 1751485694940,
                    'timestampType': 'CREATE_TIME',
                    'value': 'eyJ0dGwiOiAzNjAwfQ==',  # base64 encoded '{"ttl": 3600}'
                    'headers': []
                }
            ]
        }
    }
    
    # Test lambda handler
    result = lambda_handler(test_event, None)
    print("Lambda handler result:")
    print(json.dumps(result, indent=2))
    
    print("="*60)

if __name__ == "__main__":
    # Run tests
    test_cache_functionality()
    test_lambda_handler()
    
    print("\n\nTest completed! Check the logs above for results.")
