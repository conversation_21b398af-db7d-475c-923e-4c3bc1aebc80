import base64
import json
import logging
from DatabaseConnection import DatabaseConnection
from CacheManager import CacheManager
from CacheMonitor import CacheMonitor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fetch_and_cache_service_zones():
    """
    Fetch service zones from MongoDB and cache them using CacheManager and CacheMonitor
    """
    cache_monitor = None
    try:
        # Initialize cache monitor for performance tracking
        cache_monitor = CacheMonitor()
        logger.info("Cache monitor initialized")

        # Initialize database connection
        db_connection = DatabaseConnection()
        logger.info("Database connection established")

        # Get service zones collection
        service_zones_collection = db_connection.get_collection('servicezones')

        # Fetch all service zones
        service_zones = list(service_zones_collection.find({}))
        logger.info(f"Fetched {len(service_zones)} service zones from database")

        # Convert ObjectId to string for JSON serialization
        for zone in service_zones:
            if '_id' in zone:
                zone['_id'] = str(zone['_id'])

        # Initialize cache manager
        cache_manager = CacheManager()

        # Test cache connection
        if not cache_manager.testConnection():
            logger.error("Failed to connect to cache")
            cache_monitor.log_cache_error("connection", "Failed to connect to cache server")
            return False

        # Cache all service zones with a key
        cache_key = "service_zones:all"
        ttl = 3600  # 1 hour TTL

        success = cache_manager.setJson(cache_key, service_zones, ttl)
        if success:
            cache_monitor.log_cache_set(cache_key)
            logger.info(f"Successfully cached {len(service_zones)} service zones with key '{cache_key}'")

            # Also cache individual service zones by ID
            cached_individual_count = 0
            for zone in service_zones:
                zone_cache_key = f"service_zone:id:{zone['_id']}"
                if cache_manager.setJson(zone_cache_key, zone, ttl):
                    cache_monitor.log_cache_set(zone_cache_key)
                    cached_individual_count += 1
                else:
                    cache_monitor.log_cache_error("set", f"Failed to cache zone {zone['_id']}")

            logger.info(f"Successfully cached {cached_individual_count} individual service zones by ID")

            # Print cache performance report
            cache_monitor.print_performance_report()

            # Perform health check
            health_status = cache_monitor.check_cache_health()
            if health_status['healthy']:
                logger.info("Cache health check: HEALTHY")
            else:
                logger.warning(f"Cache health issues: {health_status['issues']}")

            return True
        else:
            cache_monitor.log_cache_error("set", f"Failed to cache service zones with key {cache_key}")
            logger.error("Failed to cache service zones")
            return False

    except Exception as e:
        if cache_monitor:
            cache_monitor.log_cache_error("general", str(e))
        logger.error(f"Error in fetch_and_cache_service_zones: {str(e)}")
        return False
    finally:
        # Close database connection
        try:
            db_connection.close_connection()
            logger.info("Database connection closed")
        except:
            pass

def get_cached_service_zones():
    """
    Retrieve cached service zones to demonstrate cache functionality
    """
    cache_monitor = None
    try:
        # Initialize cache monitor and manager
        cache_monitor = CacheMonitor()
        cache_manager = CacheManager()

        # Test cache connection
        if not cache_manager.testConnection():
            logger.error("Failed to connect to cache for retrieval")
            return None

        # Try to get all service zones from cache
        cache_key = "service_zones:all"
        cached_zones = cache_manager.getJson(cache_key)

        if cached_zones:
            cache_monitor.log_cache_hit(cache_key)
            logger.info(f"Cache HIT: Retrieved {len(cached_zones)} service zones from cache")
            return cached_zones
        else:
            cache_monitor.log_cache_miss(cache_key)
            logger.info("Cache MISS: No service zones found in cache")
            return None

    except Exception as e:
        if cache_monitor:
            cache_monitor.log_cache_error("get", str(e))
        logger.error(f"Error retrieving cached service zones: {str(e)}")
        return None

def lambda_handler(event, context):
    """
    Lambda handler that processes Kafka events and caches service zones
    """
    logger.info("Lambda function started")

    try:
        # Process Kafka event if present
        if 'records' in event:
            logger.info("Processing Kafka event")
            for topic_partition in event['records'].keys():
                records = event['records'][topic_partition]
                for record in records:
                    payload = base64.b64decode(record['value']).decode('utf-8')
                    logger.info(f"From {topic_partition}: {payload}")

                    # Parse the payload to check if it's a cache refresh request
                    try:
                        payload_data = json.loads(payload)
                        if payload_data.get('action') == 'refresh_cache' or 'ttl' in payload_data:
                            logger.info("Cache refresh requested via Kafka event")
                            fetch_and_cache_service_zones()
                    except json.JSONDecodeError:
                        logger.warning(f"Could not parse payload as JSON: {payload}")

        # Always perform cache refresh (can be modified based on requirements)
        cache_success = fetch_and_cache_service_zones()

        # Demonstrate cache retrieval
        cached_zones = get_cached_service_zones()
        cached_count = len(cached_zones) if cached_zones else 0

        if cache_success:
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'Successfully processed event and cached service zones',
                    'cached': True,
                    'cached_zones_count': cached_count,
                    'cache_retrieval_test': 'success' if cached_zones else 'failed'
                })
            }
        else:
            return {
                'statusCode': 500,
                'body': json.dumps({
                    'message': 'Event processed but caching failed',
                    'cached': False,
                    'cached_zones_count': cached_count
                })
            }

    except Exception as e:
        logger.error(f"Error in lambda_handler: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'message': f'Error processing event: {str(e)}',
                'cached': False
            })
        }
