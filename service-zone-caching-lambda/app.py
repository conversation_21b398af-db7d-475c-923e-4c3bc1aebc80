import base64
import json

test = {'eventSource': 'aws:kafka', 'eventSourceArn': 'arn:aws:kafka:us-east-2:399444019738:cluster/coddn/618a2e1a-dae2-4d8e-a247-f719fecf1bc4-2', 'bootstrapServers': 'b-2.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9098,b-1.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9098,b-3.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9098',
         'records': {'SERVICE_ZONE_CACHE_DEV-25': [{'topic': 'SERVICE_ZONE_CACHE_DEV', 'partition': 25, 'offset': 0, 'timestamp': 1751485694940, 'timestampType': 'CREATE_TIME', 'value': 'eyJ0dGwiOiAzNjAwfQ==', 'headers': []}]}}

def lambda_handler(event, context):
    print("Received Kafka Event", event)
    for topic_partition in event['records'].keys():
        records = event['records'][topic_partition]
        for record in records:
            payload = base64.b64decode(record['value']).decode('utf-8')
            print(f"From {topic_partition}: {payload}")
    return {
        'statusCode': 200,
        'body': json.dumps('Processed Kafka event')
    }
